package vn.nextpay.sbx.dto.form;

import vn.nextpay.sbx.enums.WithdrawGroupEnums;

import javax.persistence.Column;
import java.util.Date;


public class MerchantSettlementData {

    private Long merchantFk;
    private Long amount;
    private Double fee;
    private Long installmentFee;
    private Date createdDate;
    private String withdrawSubGroup;
    private WithdrawGroupEnums withdrawGroup;
    private Long totalTransaction;
    private Long transactionId;
    private String mid;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public Long getMerchantFk() {
        return merchantFk;
    }

    public void setMerchantFk(Long merchantFk) {
        this.merchantFk = merchantFk;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Double getFee() {
        return fee;
    }

    public void setFee(Double fee) {
        this.fee = fee;
    }

    public Long getInstallmentFee() {
        return installmentFee;
    }

    public void setInstallmentFee(Long installmentFee) {
        this.installmentFee = installmentFee;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getWithdrawSubGroup() {
        return withdrawSubGroup;
    }

    public void setWithdrawSubGroup(String withdrawSubGroup) {
        this.withdrawSubGroup = withdrawSubGroup;
    }

    public WithdrawGroupEnums getWithdrawGroup() {
        return withdrawGroup;
    }

    public void setWithdrawGroup(WithdrawGroupEnums withdrawGroup) {
        this.withdrawGroup = withdrawGroup;
    }

    public Long getTotalTransaction() {
        return totalTransaction;
    }

    public void setTotalTransaction(Long totalTransaction) {
        this.totalTransaction = totalTransaction;
    }
}
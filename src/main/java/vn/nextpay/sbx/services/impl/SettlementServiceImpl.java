package vn.nextpay.sbx.services.impl;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.criterion.*;
import org.hibernate.sql.JoinType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.nextpay.sbx.config.ConfigParam;
import vn.nextpay.sbx.dto.*;
import vn.nextpay.sbx.dto.form.*;
import vn.nextpay.sbx.entites.*;
import vn.nextpay.sbx.enums.*;
import vn.nextpay.sbx.repositories.dao.*;
import vn.nextpay.sbx.repositories.dao.TransactionRepository;
import vn.nextpay.sbx.services.LendingService;
import vn.nextpay.sbx.services.MerchantRevenueService;
import vn.nextpay.sbx.services.SettlementService;
import vn.nextpay.sbx.utils.DateUtil;
import vn.nextpay.sbx.utils.JSONFactory;
import vn.nextpay.sbx.utils.TingboxUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


@Service
@Transactional
public class SettlementServiceImpl implements SettlementService {
    private Logger LOGGER = LoggerFactory.getLogger(SettlementServiceImpl.class);
    private String MC_LEVEL_KEY = "MC_KEY";
    private final static long TCB_QR_MIN_FEE = 2500;
    public final static long TCB_QR_FIX_FEE = 2500;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    protected TransferPendingRepository transferPendingRepository;

    @Autowired
    protected ConfigParam configParam;

    @Autowired
    protected ParamRepository paramRepository;

    @Autowired
    private MerchantRevenueRepository merchantRevenueRepository;

    @Autowired
    private RiskMonitorRepository riskMonitorRepository;

    @Autowired
    private PromotionFeeReferenceRepository promotionFeeReferenceRepository;

    @Autowired
    private MerchantRevenueService merchantRevenueService;

    @Override
    @Transactional
    public void updateMCSettlementAndRevenueFromTrx(Date dateTo, Date dateFrom, List<Long> merchantIdList, String extData, BankTransferStatus transferStatus, String sourceCall) throws ParseException {
        TransactionSearchForm transactionSearchForm = new TransactionSearchForm();
        transactionSearchForm.setDateFrom(dateFrom);
        transactionSearchForm.setDateTo(dateTo);
        transactionSearchForm.setPageSize(Integer.MAX_VALUE);
        transactionSearchForm.setStatusList(new ArrayList<Long>() {
            {
                add(104L);
            }
        });
        transactionSearchForm.setMerchantIdList(merchantIdList);
        transactionSearchForm.setMerchantRevenuePk(0l);
        transactionSearchForm.setFeeCalculatedStatus("CALCULATED");
        transactionSearchForm.setRiskStatus(CommonStatus.APPROVED.name());
        transactionSearchForm.setApplicationUsageControl("1");
        transactionSearchForm.setRevenueStatus(Arrays.asList("PENDING", "SMALL_PENDING"));
        transactionSearchForm.setWithdrawSubGroup(WithdrawSubGroupEnum.VIETQR.name());
        if (transferStatus != null) {
            transactionSearchForm.setTransferStatus(transferStatus);
        }
        transactionSearchForm.setSourceCall(sourceCall);
        transactionSearchForm.setAmountFrom(1000L);
        // update transaction fee
        transactionSearchForm.projectProperty(
                "id", "merchantFk", "createdDate", "transferStatus", "riskStatus", "riskType", "applicationUsageControl", "withdrawSubGroup",
                "tid", "mid", "authCode", "txid", "feeCalculatedStatus", "feeInstallmentStatus", "transactionInstallmentFk", "revenueStatus",
                "amount", "issuerCode", "accquirer", "transactionType", "fee", "transactionFee", "installmentFee",
                "quickWithdraw.id", "quickWithdraw.status",
                "transactionPush.type",
                "merchant.id", "merchant.ignoreBankTransfer", "merchant.utmSource", "merchant.group", "merchant.withdrawGroup",
                "merchant.withdrawDay", "merchant.createWithdraw", "merchant.withdrawOneTrx", "merchant.merchantSubGroup","transactionAccounting.status"
        );
        List<Transaction> transactionUpdateList = transactionRepository.getWithdrawTransactionList(transactionSearchForm);
        this.updateMCSettlementAndRevenueFromTrx(transactionUpdateList, extData, sourceCall, merchantIdList, true);
    }

    @Override
    public List<Long> getListWithdrawMerchantId(CreateWithdrawForm form) {
        Criteria criteria = merchantRevenueRepository.getCriteria();

        criteria.createAlias("merchant", "merchant");
        //ChienDV Left Join MerchantSetting
        criteria.createAlias("merchant.merchantSetting", "merchantSetting", JoinType.LEFT_OUTER_JOIN);

        criteria.add(Restrictions.eq("status", MerchantRevenueStatusEnum.PENDING));
        criteria.add(Restrictions.in("withdrawSubGroup", new String[]{WithdrawSubGroupEnum.VAQR.name(), WithdrawSubGroupEnum.VIETQR.name()}));
        criteria.add(Restrictions.isNull("withdrawFk"));
        criteria.add(Restrictions.or(Restrictions.isNull("merchant.createWithdraw"),
                Restrictions.ne("merchant.createWithdraw", 0)));
        criteria.add(Restrictions.or(Restrictions.isNull("merchantSetting.noCreateWithdraw"),
                Restrictions.ne("merchantSetting.noCreateWithdraw", 1)));
        criteria.add(Restrictions.ne("merchant.status", CommonStatus.SUSPENDED));
        criteria.add(Restrictions.ne("merchant.status", CommonStatus.DENIED));
        // not inclue review: NEW, PENDING
        DetachedCriteria subIgnoreReview = DetachedCriteria.forClass(MerchantBankReview.class);
        subIgnoreReview.setProjection(Projections.property("merchantFk"));
        subIgnoreReview.add(Restrictions.in("reviewStatus", new String[]{CommonStatus.NEW.name(), CommonStatus.PENDING.name()}));
        subIgnoreReview.add(Restrictions.eq("offCreateWithdraw", 1L));
        criteria.add(Subqueries.propertyNotIn("merchant.id", subIgnoreReview));
        //
        //start not inclue mc wrong account
        DetachedCriteria subWrongAccount = DetachedCriteria.forClass(MerchantWrongAccount.class);
        subWrongAccount.setProjection(Projections.property("merchantFk"));
        subWrongAccount.add(Restrictions.eq("offCreateWithdraw", 1));
        criteria.add(Subqueries.propertyNotIn("merchant.id", subWrongAccount));
        //end
        Criterion cri = Restrictions.ne("withdrawGroup", WithdrawGroupEnums.ACCOUNTANT);
        Criterion cri1 = Restrictions.isNull("withdrawGroup");
        LogicalExpression orExp = Restrictions.or(cri, cri1);
        criteria.add(orExp);

        if(form.getFromDate() != null){
            criteria.add(Restrictions.ge("settlementDate", form.getFromDate()));
        }

        if(form.getToDate() != null){
            criteria.add(Restrictions.le("settlementDate", form.getToDate()));
        }

        if(!StringUtils.isBlank(form.getAuthoriserEmail())){
            criteria.createAlias("merchant.users", "user", JoinType.LEFT_OUTER_JOIN);
            criteria.add(Restrictions.eq("users.username", form.getAuthoriserEmail()));
        }

        /*if (!StringUtils.isBlank(form.getIssuerCode())) {
            //TriVD left join transaction
            criteria.createAlias("merchant.transactions", "transaction", JoinType.LEFT_OUTER_JOIN);
            criteria.add(Restrictions.eq("transaction.issuerCode", form.getIssuerCode()));
        }*/

        if(!StringUtils.isBlank(form.getBankAccount())){
            String sql = "merchant1_.id in (select mu1.merchant_fk from bank_account ba, mpos_user mu1 where ba.user_fk = mu1.id and ba.status = 'ACTIVE' and ba.BANK = '" + TingboxUtils.escapeSql(form.getBankAccount()) + "')";
            criteria.add(Restrictions.sqlRestriction(sql));
        }

        if(form.getIgnoreRiskMerchant() != null && form.getIgnoreRiskMerchant()){
            String sql = " this_.merchant_fk not in (SELECT DISTINCT\r\n"
                    + "    merchant_fk\r\n"
                    + " FROM\r\n"
                    + "    transaction t  LEFT JOIN MERCHANT M ON M.ID = T.MERCHANT_FK "
                    + " WHERE\r\n"
                    + "    t.revenue_status IN ('PENDING', 'SETTLE_TRANSFER')\n";
            if(!StringUtils.isBlank(form.getCheckRiskFromDate())){
                sql += "  and t.created_date >= NOW() + INTERVAL " + form.getCheckRiskFromDate() + " DAY \n";
            }
            if(form.getToDate() != null){
                SimpleDateFormat sim = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
                String todateFm = sim.format(form.getToDate());
                sql += " and to_char(t.created_date, 'YY-MM-DD HH24:MI:SS') <= '" + todateFm + "'\n";
            }
            sql += " AND ( M.MERCHANT_LEVEL IS NULL OR M.MERCHANT_LEVEL NOT IN ('KEY1', 'KEY2', 'KEY3'))";
            sql += ")";
            criteria.add(Restrictions.sqlRestriction(sql));
        }

        ProjectionList projectionList = Projections.projectionList();
        projectionList.add(Projections.distinct(Projections.property("merchantFk")));
        criteria.setProjection(projectionList);

        return criteria.list();
    }

    @Override
    public List<MerchantRevenue> getCreateWithdrawMCRevenueList(MerchantRevenueSearchForm merchantRevenueSearchForm) {
        return merchantRevenueRepository.getCreateWithdrawMCRevenueList(merchantRevenueSearchForm);
    }

    @Override
    public List<AmountWithdrawMaxDTO> getLstMerchantConfigMaxAmountWithdraw() {
        return transactionRepository.findMerchantConfigMaxAmountWithdraw();
    }

    @Override
    public Map<Long, List<RiskMonitor>> getWithdrawMerchantRefundMap(Set<Long> mIdList) {
        RiskMonitorSearchForm searchForm = new RiskMonitorSearchForm();
        searchForm.setMerchantIdSet(mIdList);
        searchForm.setWithdrawBankStatus(RefundStatus.PENDING.name());
        searchForm.setPageSize(Integer.MAX_VALUE);
        searchForm.projectProperty("id", "withdrawBackAmount", "txid", "merchantFk");
        List<RiskMonitor> riskList = riskMonitorRepository.getWithdrawRiskMoniterListBySearchForm(searchForm);
        Map<Long, List<RiskMonitor>> mapResult = new HashMap<>();
        for(RiskMonitor risk: riskList){
            List<RiskMonitor> list = mapResult.get(risk.getMerchantFk());
            if(list==null){
                list = new ArrayList<>();
                mapResult.put(risk.getMerchantFk(), list);
                list.add(risk);
            }else{
                list.add(risk);
            }
        }
        return mapResult;
    }

    /**
     * Tính phí gd và trả góp
     *
     * @param trans
     * @param feeReferenceList
     * @param feeTransList
     * @return
     * @throws Exception
     */
    @Override
    public Long calculateTransactionAndInstallmentFee(Transaction trans, List<FeeReference> feeReferenceList, List<FeeTranc> feeTransList) throws Exception {
        //phí gd
        Long transactionFee = calculateTransactionFee(trans, feeReferenceList, feeTransList);

        //phí trả góp
        if (trans.getTransactionInstallmentFk() != null) {
            transactionFee += calculateInstallmentFee(trans);
        }
        return transactionFee;
    }

    /**
     * Tính phí giao dịch
     *
     * @param trans
     * @param feeReferenceList
     * @param feeTransList
     * @return Long tranAndFee
     * @throws Exception
     */
    public Long calculateTransactionFee(Transaction trans, List<FeeReference> feeReferenceList,
                                        List<FeeTranc> feeTransList) throws Exception {
        try {
            // tinh phi
            TransactionFee transactionFee = getTransactionFee(trans, feeReferenceList, feeTransList,
                    trans.getMerchant());
            if (transactionFee == null) {
                String errorMsg = String.format("Trx not config fee. MC: {%s}, authCode: {%s}, txid: {%s}, issuerCode: {%s}, fee: {%s}, feeAmount: {%s}",
                        new Object[]{trans.getMerchant().getAuthoriserEmail(), trans.getAuthCode(), trans.getTxid(),
                                trans.getIssuerCode(), trans.getFee(), trans.getTransactionFee()});
                LOGGER.error(errorMsg);
                throw new Exception(errorMsg);
            }
            Long tranAndFee = transactionFee.getFeeAmount();
            LOGGER.debug(LendingService.class.getName() + "." + "LendingService()" + "trans: " + trans.getId()
                    + ", tranAndFee: " + tranAndFee);
            return tranAndFee;
        } catch (Exception e) {
            LOGGER.error(LendingService.class.getName() + "."
                    + "calculateTransactionFee() tính phí xảy ra lỗi transactionid: " + trans.getId().toString() + "\\n"
                    + e.getMessage() + "\\n" + e.getStackTrace() + "\\n" + e.getCause());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Tính phí trả góp
     *
     * @param transaction
     * @return Long fee
     */
    public Long calculateInstallmentFee(Transaction transaction) {
        try {
            // tinh phi
            LOGGER.debug("Transaction ID: " + transaction.getId());
            Float feeAmount = transaction.getInstallmentOutRate() * transaction.getAmount() / 100F;
            Long tranAndFee = feeAmount.longValue();

            LOGGER.debug(LendingService.class.getName() + "." + "LendingService()" + "trans: " + transaction.getId()
                    + ", tranInstallmentFee: " + tranAndFee);

            return tranAndFee;
        } catch (Exception e) {
            LOGGER.error(LendingService.class.getName() + "."
                    + "calculateInstallmentFee() tính phí xảy ra lỗi transactionid: " + transaction.getId().toString()
                    + "\\n" + e.getMessage() + "\\n" + e.getStackTrace() + "\\n" + e.getCause());
            e.printStackTrace();
            throw e;
        }
    }

    public List<MerchantRevenue> updateMCSettlementAndRevenueFromTrx(List<Transaction> transactionUpdateList, String extData, String sourceCall, List<Long> merchantIdList, boolean revenueAllTrx) throws ParseException {
        LOGGER.info("transactionUpdateList: {}", JSONFactory.toJSON(transactionUpdateList));
        Map<String, MerchantSettlementData> merchantSettlementNewMap = new HashMap<>();
        Map<String, MerchantSettlementData> onePerTrxSettlementNewMap = new HashMap<>();
        Map<String, List<Long>> mcListTransactionIdMap = new HashMap<>();
        Map<Long, Merchant> merchantMap = new HashMap<>();
        List<MerchantRevenue> merchantRevenueList = new ArrayList<>();
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat tf = new SimpleDateFormat("HHmm");
        Calendar timeRun = Calendar.getInstance();
        boolean isPvi = false;

        // MC đặc biệt -> get config
        Map<Long, TransferPending> transferPendingMap = null;
        if ("MC_THE_TIN_DUNG".equals(sourceCall) || "NEXTPAY_NL_BAOVIET".equals(sourceCall)) {
            transferPendingMap = getTransferPendingActiveMap(merchantIdList, String.valueOf(timeRun.get(Calendar.DAY_OF_WEEK)));
        }

        Map<Long, Long> buildKeyExt_Merchant_NextMap = new HashMap<>();
        for (Transaction update : transactionUpdateList) {
            Merchant merchant = update.getMerchant();
            Long merchantId = update.getMerchant().getId();
            Long maxAmountWithdraw = merchant.getMerchantSetting().getMaxAmountWithdraw();
            Long createWithdrawSunday = merchant.getMerchantSetting().getCreateWithdrawSunday();
            Long keyExtMerchantNext = buildKeyExt_Merchant_NextMap.get(merchantId);
            if(keyExtMerchantNext==null){
                keyExtMerchantNext = 0L;
                buildKeyExt_Merchant_NextMap.put(merchantId, keyExtMerchantNext);
            }
            if ("PVI".equals(update.getMerchant().getGroup())) {
                isPvi = true;
            }

            if (update.getQuickWithdraw() != null && update.getQuickWithdraw().getId() != null
                    && !update.getQuickWithdraw().getStatus().equals(CommonStatus.DENIED) && !update.isQuickTask()) {
                LOGGER.info(" ignore quick transaction. not quick task. txid:{}", update.getTxid());
                if (revenueAllTrx) {
                    throw new RuntimeException("Lỗi! GD đã tạo yêu cầu RTN, txid: " + update.getTxid());
                }
                continue;
            }

            // ko ung so tien nho, tru PVI, VAQR :))
            if (!BankTransferStatus.APPROVED.equals(update.getTransferStatus())) {
                Date cutOffVA = new SimpleDateFormat("dd-MM-yyyy").parse("25-04-2024");
                if ((!isPvi && !isVAQR(update) && update.getAmount() < configParam.TRANSFER_PENDING_MIN)
                        || (isVAQR(update) && ((update.getCreatedDate().after(cutOffVA) && update.getAmount() < configParam.TRANSFER_VA_PENDING_MIN)
                        || (update.getCreatedDate().before(cutOffVA) && update.getAmount() < configParam.TRANSFER_PENDING_MIN)))) {
                    if (revenueAllTrx) {
                        throw new RuntimeException("Lỗi! GD số tiền nhỏ, txid: " + update.getTxid());
                    }
                    continue;
                }
            }

            // check lai lan nua gd da tinh phi chua
            if (!"CALCULATED".equals(update.getFeeCalculatedStatus())
                    || (update.getTransactionInstallmentFk() != null && !"CALCULATED".equals(update.getFeeInstallmentStatus()))) {
                if (revenueAllTrx) {
                    throw new RuntimeException("Lỗi! GD chưa được tính phí, txid: " + update.getTxid());
                }
                continue;
            }
            WithdrawGroupEnums group=null;
            if(update.getTransactionAccounting()!=null && update.getTransactionAccounting().getStatus()!=null && !AccountingEnums.CANCEL.equals(update.getTransactionAccounting().getStatus())){
                group=WithdrawGroupEnums.ACCOUNTANT;
            } else if (update.getRevenueStatus().equals("SMALL_PENDING") && !isVAQR(update)){
                // khong sinh doanh thu cho cac giao dich duoi 3000 mà không phải VAQR task: 42860
                continue;
            } else {
                group=WithdrawGroupEnums.NORMALY;
            }
            // make settlement
            if (update.getMerchant().getWithdrawOneTrx() != null
                    && update.getMerchant().getWithdrawOneTrx().intValue() == 1) {
                MerchantSettlementData merchantSettlement = new MerchantSettlementData();
                merchantSettlement.setTotalTransaction(1l);
                merchantSettlement.setMid(update.getMid());
                merchantSettlement.setMerchantFk(merchantId);
                merchantSettlement.setAmount(update.getAmount());
                merchantSettlement.setFee((double) update.getTransactionFee());
                merchantSettlement.setInstallmentFee(update.getInstallmentFee() == null ? 0L : update.getInstallmentFee());
                merchantSettlement.setCreatedDate(update.getCreatedDate());
                merchantSettlement.setWithdrawSubGroup(update.getWithdrawSubGroup());
                String onePerTrxSettleKey = update.getId().toString();
                if (WithdrawGroup.HNAM_MOBILE.name().equals(merchant.getWithdrawGroup())) {
                    // đánh dấu gd thường và trả góp
                    if (update.getTransactionInstallmentFk() != null) {
                        onePerTrxSettleKey = onePerTrxSettleKey + "_INSTALLMENT";
                    } else {
                        onePerTrxSettleKey = onePerTrxSettleKey + "_NORMAL";
                    }
                }
                merchantSettlement.setWithdrawGroup(group);
                onePerTrxSettlementNewMap.put(onePerTrxSettleKey, merchantSettlement);
            } else {
                // get key
                String settleMapKey = getSettleMapKey(update, merchant, df, tf, transferPendingMap, timeRun, sourceCall);
                String settleMapKeyExt = settleMapKey + keyExtMerchantNext;

                String withdrawSubGroup = update.getWithdrawSubGroup();
                MerchantSettlementData merchantSettlement = merchantSettlementNewMap.get(settleMapKeyExt);

                // tach revenue theo Vol
                if(merchantSettlement !=null && maxAmountWithdraw!=null && maxAmountWithdraw>0){
                    Long withdrawAmount = merchantSettlement.getAmount() - merchantSettlement.getFee().longValue() - merchantSettlement.getInstallmentFee();
                    Long amountAfterAddTrx = withdrawAmount +
                            (update.getAmount() - update.getTransactionFee() - (update.getInstallmentFee() == null ? 0L : update.getInstallmentFee()));
                    if(amountAfterAddTrx > maxAmountWithdraw){
                        // next merchant settlement
                        merchantSettlement = null;
                        // next key
                        keyExtMerchantNext = keyExtMerchantNext + 1;
                        buildKeyExt_Merchant_NextMap.put(merchantId, keyExtMerchantNext);
                        settleMapKeyExt = settleMapKey + keyExtMerchantNext;
                    }
                }

                if (merchantSettlement != null) {
                    merchantSettlement.setAmount(merchantSettlement.getAmount() + update.getAmount());
                    merchantSettlement.setFee(merchantSettlement.getFee() + update.getTransactionFee());
                    merchantSettlement.setInstallmentFee(merchantSettlement.getInstallmentFee()
                            + (update.getInstallmentFee() == null ? 0L : update.getInstallmentFee()));
                    merchantSettlement.setWithdrawGroup(group);
                    merchantSettlement.setTotalTransaction(merchantSettlement.getTotalTransaction() + 1);
                    mcListTransactionIdMap.get(settleMapKeyExt).add(update.getId());
                } else {
                    // new settlement
                    merchantSettlement = new MerchantSettlementData();
                    merchantSettlement.setTotalTransaction(1l);
                    merchantSettlement.setMid(update.getMid());
                    merchantSettlement.setMerchantFk(merchantId);
                    merchantSettlement.setAmount(update.getAmount());
                    merchantSettlement.setFee((double) update.getTransactionFee());
                    merchantSettlement.setInstallmentFee(update.getInstallmentFee() == null ? 0L : update.getInstallmentFee());
                    merchantSettlement.setCreatedDate(update.getCreatedDate());
                    merchantSettlement.setWithdrawGroup(group);
                    merchantSettlement.setWithdrawSubGroup(withdrawSubGroup);
                    merchantSettlementNewMap.put(settleMapKeyExt, merchantSettlement);

                    // new list transaction
                    List<Long> mcListTransaction = new ArrayList<Long>();
                    mcListTransaction.add(update.getId());
                    mcListTransactionIdMap.put(settleMapKeyExt, mcListTransaction);
                }
            }
            // Merchant map
            if (merchantMap.get(merchantId) == null) {
                merchantMap.put(merchantId, update.getMerchant());
            }
        }

        // update DB
        for (Map.Entry<String, MerchantSettlementData> settleEntry : merchantSettlementNewMap.entrySet()) {

            String extDataInsert = extData;
            String keymap = settleEntry.getKey();
            MerchantSettlementData settle = settleEntry.getValue();
            Long mcId = settle.getMerchantFk();
            Merchant merchant = merchantMap.get(mcId);
            if (WithdrawGroup.HNAM_MOBILE.name().equals(merchant.getWithdrawGroup())) {
                extDataInsert = (StringUtils.isBlank(extDataInsert) ? "" : extDataInsert + "_") + keymap.split("_")[2];
            }
            // insert merchant revenue
            MerchantRevenue revenue = createMerchantRevenue(merchant, settle, extDataInsert);

            // update trx
            int numberUpdate = daoTransaction.updateMerchantSettlementFk(mcListTransactionIdMap.get(keymap), settle.getId());
            LOGGER.info("updateMCSettlementAndRevenueFromTrx numberUpdate: " + numberUpdate + ", mcListTransactionIdMap.get(keymap) size: " + mcListTransactionIdMap.get(keymap).size());

            merchantRevenueList.add(revenue);
        }

        // one per trx
        for (Map.Entry<String, MerchantSettlementData> oneSettleEntry : onePerTrxSettlementNewMap.entrySet()) {

            String extDataInsert = extData;
            String[] onePerTrxSettleKey = oneSettleEntry.getKey().split("_");
            Long trxId = Long.parseLong(onePerTrxSettleKey[0]);
            MerchantSettlementData settle = oneSettleEntry.getValue();
            Long mcId = settle.getMerchantFk();
            Merchant merchant = merchantMap.get(mcId);
            if (WithdrawGroup.HNAM_MOBILE.name().equals(merchant.getWithdrawGroup())) {
                extDataInsert = (StringUtils.isBlank(extDataInsert) ? "" : extDataInsert + "_") + onePerTrxSettleKey[1];
            }

            // insert merchant revenue
            MerchantRevenue revenue = createMerchantRevenue(merchantMap.get(mcId), settle, extDataInsert);

            // update trx
            daoTransaction.updateMerchantSettlementFk(trxId, settle.getId());

            merchantRevenueList.add(revenue);
        }

        return merchantRevenueList;
    }

    private void createRevenueAndUpdateTransactionForBatch(String extData, List<MerchantSettlementData> batch, Merchant merchant) {
        List<MerchantRevenue> merchantRevenueList = new ArrayList<>();
        String extDataInsert = extData;
        MerchantRevenue revenue = merchantRevenueService.createMerchantRevenueForBatchTransaction(
                merchant, batch, extDataInsert);
        merchantRevenueList.add(revenue);
        for (MerchantSettlementData settle : batch) {
            Long trxId = settle.getTransactionId();
            transactionRepository.updateMerchantRevenueFk(trxId, revenue.getId());
        }
    }

    @Override
    public Map<Long, TransferPending> getTransferPendingActiveMap(List<Long> merchantIdList, String dayOfWeek) {
        List<TransferPending> transferList = transferPendingRepository.getTransferPendingActiveList(merchantIdList, dayOfWeek);
        Map<Long, TransferPending> transferMap = new HashMap<Long, TransferPending>();
        for (TransferPending transferPending : transferList) {
            transferMap.put(transferPending.getMerchantFk(), transferPending);
        }
        return transferMap;
    }

    @Override
    public Map<Long, Date> getcutoffTimeMap(String timeRunString, Map<Long, TransferPending> transferPendingMap) {
        Map<Long, Date> cutoffTimeMap = new HashMap<Long, Date>();
        for (Map.Entry<Long, TransferPending> entry : transferPendingMap.entrySet()) {
            Long merchantId = entry.getKey();
            TransferPending transfer = entry.getValue();
            Date cutoffTime = getCutoffTimeMCTheTinDung(timeRunString, transfer);
            cutoffTimeMap.put(merchantId, cutoffTime);
        }
        return cutoffTimeMap;
    }

    @Override
    public Map<Long, WithdrawScheduled> getWithdrawScheduledActiveMap(List<Long> merchantIdList) {
        Criteria criteria = transactionRepository.currentSession().createCriteria(WithdrawScheduled.class);
        criteria.createAlias("scheduledGroup", "scheduledGroup", JoinType.LEFT_OUTER_JOIN);
        criteria.createAlias("merchant", "merchant", JoinType.LEFT_OUTER_JOIN);
        criteria.add(Restrictions.eq("status", CommonStatus.ACTIVE));
        criteria.add(Restrictions.eq("scheduledGroup.status", CommonStatus.ACTIVE));
        if(merchantIdList!=null && !merchantIdList.isEmpty()){
            criteria.add(Restrictions.in("merchantFk", merchantIdList));
        }
        SearchFormBase searchForm = new SearchFormBase();
        searchForm.projectProperty("id", "merchant.id", "scheduledGroup.id", "scheduledGroup.scheduledDay", "scheduledGroup.scheduledTime", "scheduledGroup.withdrawDay");
        transactionRepository.applyProjection(criteria, searchForm, WithdrawScheduled.class);
        List<WithdrawScheduled> scheduledList = criteria.list();
        Map<Long, WithdrawScheduled> scheduledMap = new HashMap<Long, WithdrawScheduled>();
        for (WithdrawScheduled scheduled : scheduledList) {
            scheduledMap.put(scheduled.getMerchant().getId(), scheduled);
        }
        return scheduledMap;
    }

    private boolean isVAQR(Transaction update) {
        return (update != null && update.getTransactionPush() != null && TransactionPushType.VAQR.equals(update.getTransactionPush().getType()));
    }

    private String getSettleMapKey(Transaction update, Merchant merchant, SimpleDateFormat dateFormat,
                                   SimpleDateFormat tf, Map<Long, TransferPending> transferPendingMap, Calendar timeRun, String sourceCall) {
        String settleMapKey = merchant.getId().toString();
        if ((MerchantGroup.MC_THE_TIN_DUNG.name().equals(merchant.getGroup())  && transferPendingMap!=null)
                || "MPOST0_PVI".equals(sourceCall)
                || ("NGANLUONG".equals(merchant.getUtmSource()) && "BaoVietBank".equals(merchant.getMerchantSubGroup()))) {

            // Tách phiếu trước và sau cutoff time
            Date cutoffTime = null;
            if (MerchantGroup.MC_THE_TIN_DUNG.name().equals(merchant.getGroup())) {
                TransferPending transPending = transferPendingMap.get(merchant.getId());
                if (transPending == null) return settleMapKey + "_AFTER";
                cutoffTime = getCutoffTimeMCTheTinDung(tf.format(timeRun.getTime()), transPending);
            } else
            if ("NGANLUONG".equals(merchant.getUtmSource()) && "BaoVietBank".equals(merchant.getMerchantSubGroup())) {
                Calendar cal14h20 = Calendar.getInstance();
                cal14h20.set(Calendar.HOUR_OF_DAY, 14);
                cal14h20.set(Calendar.MINUTE, 20);
                cal14h20.set(Calendar.SECOND, 0);
                cal14h20.set(Calendar.MILLISECOND, 0);
                cutoffTime = cal14h20.getTime();
            } else {
                Calendar cal15h = Calendar.getInstance();
                cal15h.set(Calendar.HOUR_OF_DAY, 15);
                cal15h.set(Calendar.MINUTE, 0);
                cal15h.set(Calendar.SECOND, 0);
                cal15h.set(Calendar.MILLISECOND, 0);
                cutoffTime = cal15h.getTime();
            }
            if (update.getCreatedDate().before(cutoffTime)) {
                settleMapKey = settleMapKey + "_" + dateFormat.format(update.getCreatedDate()) + "_BEFORE";
            } else {
                settleMapKey = settleMapKey + "_" + dateFormat.format(update.getCreatedDate()) + "_AFTER";
            }
        } else {
            // tách riêng theo ngày
            settleMapKey = settleMapKey + "_" + dateFormat.format(update.getCreatedDate());

            if (WithdrawGroup.HNAM_MOBILE.name().equals(merchant.getWithdrawGroup())) {
                // tách phiếu gd thường và trả góp
                if (update.getTransactionInstallmentFk() != null) {
                    settleMapKey = settleMapKey + "_INSTALLMENT";
                } else {
                    settleMapKey = settleMapKey + "_NORMAL";
                }
            }

            // @VietQR_PHIEU tach revenue theo gio
            if(!StringUtils.isBlank(update.getWithdrawSubGroup())){
                String hourPrefix = DateUtil.dfDDHH.format(update.getCreatedDate());
                settleMapKey =  String.format("%s_%s_%s", settleMapKey, hourPrefix, update.getWithdrawSubGroup());
            }

            if(MerchantGroup.BAOVIET.name().equals(update.getMerchant().getGroup())){
                String hourPrefix = DateUtil.dfDDHH.format(update.getCreatedDate());
                settleMapKey =  String.format("%s_%s_BAOVIET", settleMapKey, hourPrefix);
            }

            // cutoff time 14h20: PGBANK_NGANLUONG
            if(MerchantSubGroup.PGBANK_NGANLUONG.name().equals(merchant.getMerchantSubGroup())){
                WithdrawCutOffHHmm cutOffHHmm = getWithdrawPGBankCutOff();
                if(cutOffHHmm!=null){
                    Calendar calTransactionDate = Calendar.getInstance();
                    calTransactionDate.setTime(update.getCreatedDate());
                    if(calTransactionDate.get(Calendar.HOUR_OF_DAY) * 60 + calTransactionDate.get(Calendar.MINUTE)
                            < cutOffHHmm.getCutoffHour()*60 + cutOffHHmm.getCutoffMinute()
                    ){
                        settleMapKey = settleMapKey + "_" + dateFormat.format(update.getCreatedDate()) + "_BEFORE";
                    }else{
                        settleMapKey = settleMapKey + "_" + dateFormat.format(update.getCreatedDate()) + "_AFTER";
                    }
                }
            }
        }
        if(update.getTransactionAccounting()!=null && update.getTransactionAccounting().getStatus()!=null && !AccountingEnums.CANCEL.equals(update.getTransactionAccounting().getStatus())){
            settleMapKey = settleMapKey+"_"+WithdrawGroupEnums.ACCOUNTANT;
        }else{
            settleMapKey = settleMapKey + "_"+WithdrawGroupEnums.NORMALY;
        }
        return settleMapKey;
    }

    private Param withdrawPGBankCutOff = null;
    public WithdrawCutOffHHmm getWithdrawPGBankCutOff(){
        if(withdrawPGBankCutOff==null){
            withdrawPGBankCutOff = paramRepository.findParam("PGBANK_NGANLUONG", "WITHDRAW_HOUR_CUTOFF_HHMM");
        }
        if(withdrawPGBankCutOff!=null && !StringUtils.isBlank(withdrawPGBankCutOff.getValue())){
            String HHmm = withdrawPGBankCutOff.getValue();
            int hourCutoff = Integer.parseInt(HHmm.substring(0, 2));
            int minuteCutoff = Integer.parseInt(HHmm.substring(2));
            return new WithdrawCutOffHHmm(hourCutoff, minuteCutoff);
        }
        return null;
    }

    public Date getCutoffTimeMCTheTinDung(String timeRun, TransferPending transferPending) {
        String time1 = transferPending.getTime1();
        String time2 = transferPending.getTime2();
        String time3 = transferPending.getTime3();
        if (StringUtils.isBlank(time2)) { // Thanh toán ngày 1 lần
            return getCutoffOneTime(timeRun, time1);
        } else if (StringUtils.isBlank(time3)) { // TT ngày 2 lần
            return getCutoffTwoTime(timeRun, time1, time2);
        } else { // TT ngày 3 lần
            return getCutoffThreeTime(timeRun, time1, time2, time3);
        }
    }

    private Date getCutoffThreeTime(String timeRunString, String time1, String time2, String time3) {
        Calendar cutoffTime = Calendar.getInstance();
        if (timeRunString.compareTo(time1) < 0) { // lấy cutoff hôm trước
            cutoffTime.add(Calendar.DATE, -1);
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time3.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time3.substring(2, 4)));
        } else if (timeRunString.compareTo(time1) >= 0
                && timeRunString.compareTo(time2) < 0) { // lấy time1
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time1.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time1.substring(2, 4)));
        } else if (timeRunString.compareTo(time2) >= 0
                && timeRunString.compareTo(time3) < 0) { // lấy time2
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time2.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time2.substring(2, 4)));
        } else { // lấy time3
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time3.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time3.substring(2, 4)));
        }
        cutoffTime.set(Calendar.SECOND, 0);
        cutoffTime.set(Calendar.MILLISECOND, 0);
        return cutoffTime.getTime();
    }

    private Date getCutoffTwoTime(String timeRunString, String time1, String time2) {
        Calendar cutoffTime = Calendar.getInstance();
        if (timeRunString.compareTo(time1) < 0) { // lấy cutoff hôm trước
            cutoffTime.add(Calendar.DATE, -1);
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time2.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time2.substring(2, 4)));
        } else if (timeRunString.compareTo(time1) >= 0
                && timeRunString.compareTo(time2) < 0) { // lấy time1
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time1.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time1.substring(2, 4)));
        } else { // lấy time2
            cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time2.substring(0, 2)));
            cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time2.substring(2, 4)));
        }
        cutoffTime.set(Calendar.SECOND, 0);
        cutoffTime.set(Calendar.MILLISECOND, 0);
        return cutoffTime.getTime();
    }

    private Date getCutoffOneTime(String timeRunString, String time1) {
        Calendar cutoffTime = Calendar.getInstance();
        if (timeRunString.compareTo(time1) < 0) { // lấy cutoff hôm trước
            cutoffTime.add(Calendar.DATE, -1);
        }
        cutoffTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(time1.substring(0, 2)));
        cutoffTime.set(Calendar.MINUTE, Integer.parseInt(time1.substring(2, 4)));
        cutoffTime.set(Calendar.SECOND, 0);
        cutoffTime.set(Calendar.MILLISECOND, 0);
        return cutoffTime.getTime();
    }

    public TransactionFee getTransactionFee(Transaction trx, List<FeeReference> feeReferenceList, List<FeeTranc> feeTrancList, Merchant merchant) {
        // fee apply
        TransactionFee transactionFee = null;
        PromotionFeeReference referencePromotionFee = null; // phi khuyen mai
        TransactionType transactionType = TransactionType.NORMAL;
        PaymentMethod paymentMethod = PaymentMethod.findPaymentMethod(trx.getAccquirer());
        if (trx.getTransactionType()!=null && trx.getTransactionType().equals(TransactionType.MOTO)) {
            paymentMethod = PaymentMethod.MOTO;
        }
        if(paymentMethod==null){
            LOGGER.error("not payment method for trx: txid:{} - accquirer: {}", trx.getTxid(), trx.getAccquirer());
            throw new RuntimeException("No payment method definded for txid: " + trx.getTxid());
        }
        if (trx.getTransactionInstallment() != null && trx.getTransactionInstallment().getId() != null) {
            transactionType = TransactionType.INSTALLMENT;
        }

        // apply promotion ?
        boolean applyPromotion = false;
        boolean intimePromotion = false;
        String promotionVipName = "VIP";
        boolean isPromotionVip = false;
        PromotionFee promotionFee = null;
        int promotionLevel = 0;
        while (promotionLevel < 2) {
            promotionLevel++;
            if (isPromotionVip || applyPromotion) {
                break;
            }
            if (promotionLevel == 1) {
                promotionFee = merchant.getPromotionFee();
            }
            if (promotionLevel == 2) {
                promotionFee = merchant.getPromotionFee2();
            }
            if (promotionFee == null || promotionFee.getId() == null) {
                continue;
            }
            if (promotionVipName.equalsIgnoreCase(promotionFee.getName())) {
                isPromotionVip = true;
            }
            if (promotionFee.getDayAfterReg() != null && promotionFee.getDayAfterReg() > 0) {
                // promotion: day after registration
                long dayAfterReg = promotionFee.getDayAfterReg();
                Calendar calendar = Calendar.getInstance();
                if (merchant.getPromotionActivedDate() != null) {
                    calendar.setTime(merchant.getPromotionActivedDate());
                } else {
                    calendar.setTime(merchant.getCreatedDate());
                }
                calendar.add(Calendar.DATE, (int) dayAfterReg);
                if (calendar.getTime().compareTo(trx.getCreatedDate()) >= 0) {
                    intimePromotion = true;
                    List<PromotionFeeReference> promotionRefs = promotionFeeReferenceRepository.getPromotionFeeReferenceListByPromotionFeeId(promotionFee.getId());
                    for (PromotionFeeReference promotionRef : promotionRefs) {
                        Issuer issuer = promotionRef.getIssuer();
                        if (promotionRef.getIssuer().getCode().equals(trx.getIssuerCode()) && paymentMethod.equals(issuer.getPaymentMethod())) {
                            applyPromotion = true;
                            referencePromotionFee = promotionRef;
                        }
                    }
                }
            } else {
                if (promotionFee.getDateFrom() != null && promotionFee.getDateTo() != null) {
                    if (trx.getCreatedDate().compareTo(promotionFee.getDateFrom()) >= 0
                            && trx.getCreatedDate().compareTo(promotionFee.getDateTo()) <= 0) {
                        intimePromotion = true;
                        List<PromotionFeeReference> promotionRefs = promotionFeeReferenceRepository.getPromotionFeeReferenceListByPromotionFeeId(promotionFee.getId());
                        for (PromotionFeeReference promotionRef : promotionRefs) {
                            Issuer issuer = promotionRef.getIssuer();
                            if (promotionRef.getIssuer().getCode().equals(trx.getIssuerCode()) && paymentMethod.equals(issuer.getPaymentMethod())) {
                                applyPromotion = true;
                                referencePromotionFee = promotionRef;
                            }
                        }
                    }
                }
            }
            if (promotionFee.getAmount() != null && promotionFee.getAmount() > 0) {
                applyPromotion = false;
                if (promotionFee.getDayAfterReg() == null && promotionFee.getDateFrom() == null && promotionFee.getDateTo() == null) {
                    intimePromotion = true;
                }
                if (intimePromotion) {
                    long amount = this.sumAmountByMerchant(trx.getMerchantFk());
                    if (amount + trx.getAmount() < promotionFee.getAmount()) {
                        List<PromotionFeeReference> promotionRefs = promotionFeeReferenceRepository.getPromotionFeeReferenceListByPromotionFeeId(promotionFee.getId());
                        for (PromotionFeeReference promotionRef : promotionRefs) {
                            Issuer issuer = promotionRef.getIssuer();
                            if (promotionRef.getIssuer().getCode().equals(trx.getIssuerCode()) && paymentMethod.equals(issuer.getPaymentMethod())) {
                                applyPromotion = true;
                                referencePromotionFee = promotionRef;
                            }
                        }
                    }
                }
            }
        }

        if (TransactionType.PROMOTION.equals(trx.getTransactionType())) {
            // set 0 fee
            float percentageFee = 0f;
            transactionFee = new TransactionFee(0, percentageFee, String.format("%s-%s-%s", "FEE_PROMOTION", trx.getIssuerCode(), paymentMethod.name()));
            transactionFee.setGroupFee("GD_PROMOTION");
        } else if (applyPromotion && !isPromotionVip) {
            float percentageFee = referencePromotionFee.getFee();
            if (transactionType.equals(TransactionType.INSTALLMENT) && referencePromotionFee.getFeeInstallment() != null) {
                percentageFee = referencePromotionFee.getFeeInstallment();
            }
            transactionFee = new TransactionFee(0, percentageFee, String.format("%s-%s-%s", "FEE_REF_PROMOTION", referencePromotionFee.getIssuer().getCode(), paymentMethod.name()));
            transactionFee.setGroupFee("PROMOTION_FK");
        } else {
            // apply fee
            for (FeeReference feeRef : feeReferenceList) {
                Issuer issuer = feeRef.getIssuer();

                // Task 49823: với MC đặc biệt -> phí cấu hình theo: issuer code + paymentMethod + accquirer
                {
                    if (issuer.getCode().equals(trx.getIssuerCode())
                            && paymentMethod.equals(issuer.getPaymentMethod())
                            && StringUtils.isBlank(feeRef.getAcquirer())) {
                        float percentageFee = feeRef.getFeePerTransaction();
                        if (transactionType.equals(TransactionType.INSTALLMENT) && feeRef.getFeeInstallment() != null) {
                            percentageFee = feeRef.getFeeInstallment();
                        }
                        transactionFee = new TransactionFee(0, percentageFee, String.format("%s-%s-%s", "FEE_REF_" + transactionType, issuer.getCode(), paymentMethod.name()));
                        transactionFee.setGroupFee("ONE_MERCHANT");
                        break;
                    }
                }
            }
        }

        if (transactionFee == null) {
            // app dung phi mac dinh he thong
            for (FeeTranc feeRef : feeTrancList) {
                Issuer issuer = feeRef.getIssuer();
                if (issuer.getCode().equals(trx.getIssuerCode())
                        && paymentMethod.equals(issuer.getPaymentMethod())) {
                    float percentageFee = feeRef.getValue();
                    if (transactionType.equals(TransactionType.INSTALLMENT) && feeRef.getInstallmentFee() != null) {
                        percentageFee = feeRef.getInstallmentFee();
                    }
                    transactionFee = new TransactionFee(0, percentageFee, String.format("%s-%s-%s", "FEE_DEFAULT", issuer.getCode(), paymentMethod.name()));
                    transactionFee.setGroupFee("BASE_SOURCE");
                    break;
                }
            }
        }
        if (transactionFee != null) {
            Float feeAmount = transactionFee.getFlatFee() + transactionFee.getPercentageFee() * trx.getAmount() / 100F;
            transactionFee.setFeeAmount(feeAmount.longValue());
        }

        // GD TCB qr có min fee là 2500
        // NL fix fee = 2500
        if (trx.getTransactionPush() != null && TransactionPushType.TCB.equals(trx.getTransactionPush().getType())) {
            if(UtmSourceEnum.NGANLUONG.name().equals(merchant.getUtmSource())){
                if(StringUtils.isBlank(merchant.getGroup()) || (!"VCB_NGAN_LUONG".equals(merchant.getGroup()) && !"MERCHANT_VCB_NL".equals(merchant.getGroup()))){
                    transactionFee.setFeeAmount(TCB_QR_FIX_FEE);
                    transactionFee.setGroupFee("TCBQR_NGANLUONG");
                }
            }else{// nextpay
                if (transactionFee != null && transactionFee.getFeeAmount() < TCB_QR_MIN_FEE && transactionFee.getPercentageFee()>0f) {
                    transactionFee.setFeeAmount(TCB_QR_MIN_FEE);
                    transactionFee.setGroupFee("TCBQR_NEXTPAY");
                }
            }
        }

        // uu dai phi VIETQR(1504)
        boolean isMCKey = false;
        if(MC_LEVEL_KEY.equals(merchant.getMcLevelMynp())){
            isMCKey = true;
        }

        if(trx.getTransactionPush() != null && TransactionPushType.VAQR.equals(trx.getTransactionPush().getType())) {
            if (merchant.getMerchantSetting().getFeeVietQrNew() != null && merchant.getMerchantSetting().getFeeVietQrNew() != 0 && configParam.paramVietQrOfferFee != null) {
                if(merchant.getMerchantSetting().getFeeVietQrNew() == TransactionFeeNew.VIETQR2504.getFeeCode()) {
                    ParamVietQrOfferFee paramVietQR = configParam.paramVietQrOfferFee;
                    transactionFee.setGroupFee("VAQR_UU_DAI_THUONG");
                    if (isMCKey && paramVietQR.getMcKeyFee() != null) {
                        paramVietQR = paramVietQR.getMcKeyFee();
                        transactionFee.setGroupFee("VAQR_UU_DAI_KEY");
                    }
                    if (trx.getAmount() < paramVietQR.getAmount()) {
                        Float feeAmount = 0 + paramVietQR.getPercentageFee() * trx.getAmount() / 100F;
                        transactionFee.setFeeAmount(feeAmount.longValue());
                        transactionFee.setPercentageFee(paramVietQR.getPercentageFee());
                    } else {
                        transactionFee.setFeeAmount(paramVietQR.getFlatFee());
                        float percentFee = transactionFee.getFeeAmount() * 100f / trx.getAmount();
                        percentFee = Math.round(percentFee * 1000) / 1000f;
                        transactionFee.setPercentageFee(percentFee);
                    }
                    transactionFee.setCtNewFee(TransactionFeeNew.VIETQR2504.name());
                } else if (merchant.getMerchantSetting().getFeeVietQrNew() == TransactionFeeNew.VIETQR2508T1.getFeeCode()){
                    // CT 2508(T+1)
                    ParamVietQrOfferFee paramVietQR = configParam.paramVietQrOfferFee.getCt1();
                    Float feeAmount = paramVietQR.getFlatFee() + paramVietQR.getPercentageFee() * trx.getAmount() / 100F;
                    transactionFee.setFeeAmount(feeAmount.longValue());
                    transactionFee.setPercentageFee(paramVietQR.getPercentageFee());
                    transactionFee.setCtNewFee(TransactionFeeNew.VIETQR2508T1.name());
                    transactionFee.setGroupFee("VIETQR2508T1");
                } else if (merchant.getMerchantSetting().getFeeVietQrNew() == TransactionFeeNew.VIETQR2508T2.getFeeCode()){
                    // CT 2508(T+2)
                    ParamVietQrOfferFee paramVietQR = configParam.paramVietQrOfferFee.getCt2();
                    Float feeAmount = paramVietQR.getFlatFee() + paramVietQR.getPercentageFee() * trx.getAmount() / 100F;
                    transactionFee.setFeeAmount(feeAmount.longValue());
                    transactionFee.setPercentageFee(paramVietQR.getPercentageFee());
                    transactionFee.setCtNewFee(TransactionFeeNew.VIETQR2508T2.name());
                    transactionFee.setGroupFee("VIETQR2508T2");
                } else if (merchant.getMerchantSetting().getFeeVietQrNew() == TransactionFeeNew.VIETQRT1_RTN_T0.getFeeCode()) {
                    // CT T1_RTN_T0
                    transactionFee.setGroupFee("VIETQRT1_RTN_T0");
                    ParamVietQrOfferFee paramVietQR = configParam.paramVietQrOfferFee.getT1_rtn_t0();
                    Long flatFee = paramVietQR.getFlatFee();
                    Float percentFee = paramVietQR.getPercentageFee();
                    if (merchant.getPaymentNowVietQrTpv() != null && merchant.getPaymentNowVietQrTpv().getFlatfee() != null) {
                        flatFee = merchant.getPaymentNowVietQrTpv().getFlatfee();
                        percentFee = 0F;
                        transactionFee.setGroupFee("VIETQRT1_RTN_T0_VUOT_TPV");
                    }
                    Float feeAmount = flatFee + percentFee * trx.getAmount() / 100F;
                    transactionFee.setFeeAmount(feeAmount.longValue());
                    transactionFee.setPercentageFee(percentFee);
                    transactionFee.setCtNewFee(TransactionFeeNew.VIETQRT1_RTN_T0.name());
                }
            }
        }


        // uu_dai_bank_on_us
        /* - issuerList: VISA_LOCAL_ON, MASTER_LOCAL_ON, JCB_LOCAL_ON
           - setting bank onus - feeIncentivesApply contains [cardBank]
           - new setting: merchantSetting.cs1504FeeOnUs == 1
           - Chi GD thuong
         */
        if(!StringUtils.isBlank(merchant.getMerchantSetting().getFeeIncentivesApply())
                && (trx.getTransactionInstallment() == null || trx.getTransactionInstallment().getId()==null)
                && merchant.getMerchantSetting().getCs1504FeeOnUs()!=null && merchant.getMerchantSetting().getCs1504FeeOnUs()==1) {
            if (configParam.paramBankOnusFee != null && configParam.paramBankOnusFee.getIssuerCodeList().contains(String.format("#%s#", trx.getIssuerCode()))) {
                ParamBankOnusFee paramBankOnus = configParam.paramBankOnusFee;
                List<ParamBankOnusFee.IssuerFee> issuerFeeList = paramBankOnus.getIssuerList();
                transactionFee.setGroupFee("BANK_ONUS_UU_DAI_THUONG");
                if(isMCKey && paramBankOnus.getMcKeyIssuerList()!=null){
                    issuerFeeList = paramBankOnus.getMcKeyIssuerList();
                    transactionFee.setGroupFee("BANK_ONUS_UU_DAI_MCKEY");
                }
                String cardBank = trx.getCardBank();
                if(cardBank!=null && merchant.getMerchantSetting().getFeeIncentivesApply().contains(cardBank)) {
                    for (ParamBankOnusFee.IssuerFee issuerFee : issuerFeeList) {
                        if (issuerFee.getBank().equals(cardBank)) {
                            Float feeAmount = (issuerFee.getFlatFee() != null ? issuerFee.getFlatFee() : 0L) + issuerFee.getPercentageFee() * trx.getAmount() / 100F;
                            transactionFee.setFeeAmount(feeAmount.longValue());
                            transactionFee.setPercentageFee(issuerFee.getPercentageFee());
                            break;
                        }
                    }
                }
            }
        }

        // GD deposit lay fee(old)
        if(TransactionType.DEPOSIT.equals(trx.getTransactionType()) && trx.getFee()!=null){
            transactionFee.setPercentageFee(trx.getFee());
            Float feeAmount = transactionFee.getFlatFee() + transactionFee.getPercentageFee() * trx.getAmount() / 100F;
            transactionFee.setFeeAmount(feeAmount.longValue());
            transactionFee.setGroupFee("GD_DEPOSIT");
        }

        return transactionFee;
    }

    private long sumAmountByMerchant(Long merchantId) {
        Criteria criteria = transactionRepository.getCriteria();
        criteria.add(Restrictions.eq("merchantFk", merchantId));
        criteria.add(Restrictions.isNotNull("transactionFee"));
        criteria.add(Restrictions.eq("status", 104L));
        criteria.setProjection(Projections.sum("amount"));
        Object rs = criteria.uniqueResult();
        if (rs == null) {
            return 0l;
        }
        return (Long) rs;
    }
}
